import 'package:flutter/material.dart';
import 'package:vp_common/generated/l10n.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

Future<SubAccountModel?> showDerivativeSubAccountBottomSheet({
  required BuildContext context,
  List<SubAccountModel>? listSubAccounts,
}) async {
  if (listSubAccounts?.isEmpty ?? true) {
    listSubAccounts =
        GetIt.instance<SubAccountCubit>().getSubAccountsAllTransfer;
  }
  final colorUtils = Theme.of(context);
  return await showModalBottomSheet<SubAccountModel>(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (BuildContext context) {
      return SafeArea(
        bottom: true,
        child: Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 24),
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height / 2,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: colorUtils.bgPopup,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ListView.builder(
                        itemCount: listSubAccounts?.length,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemBuilder: (context, index) {
                          return BottomSheetItemWidget(
                            text:
                                listSubAccounts?[index]
                                    .toSubAccountType
                                    .displayName ??
                                "-",
                            onTap: () {
                              Navigator.pop(context, listSubAccounts?[index]);
                            },
                            border: true,
                          );
                        },
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                ButtonBottomSheet(
                  color: colorUtils.red,
                  text: VPCommonLocalize.current.cancel,
                  onTap: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
        ),
      );
    },
  );
}
