import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'package:vp_common/extensions/date_extensions.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';

class VerticalDatePickerView extends StatefulWidget {
  const VerticalDatePickerView({
    super.key,
    this.minDate,
    this.maxDate,
    this.startDate,
    this.endDate,
    this.rangeDay,
    this.endDateAtEndOfDay = true,
  });

  final DateTime? minDate;

  final DateTime? maxDate;

  final DateTime? startDate;

  final DateTime? endDate;

  /// Số ngày cho phép chọn trước/sau ngày bắt đầu chọn
  final int? rangeDay;

  /// true: 2025-19-07 23:59:59
  /// false: 2025-19-07 00:00:00
  final bool endDateAtEndOfDay;

  @override
  VerticalDatePickerViewState createState() => VerticalDatePickerViewState();
}

class VerticalDatePickerViewState extends State<VerticalDatePickerView> {
  PickerDateRange? _selectedRange;

  DateTime? _minDate;

  DateTime? _maxDate;

  bool _userStartedSelection = false;

  final DateRangePickerController _controller = DateRangePickerController();

  @override
  void initState() {
    super.initState();

    // Set the initial display date for the SfDateRangePicker.
    // If a start date has already been selected (_selectedRange.startDate),
    // the picker will automatically scroll to the month containing that date
    // when opened. This improves the user experience by avoiding manual scrolling.
    if (widget.startDate != null) {
      _controller.displayDate = widget.startDate;
    }

    if (widget.startDate != null && widget.endDate != null) {
      _selectedRange = PickerDateRange(widget.startDate, widget.endDate);
    }

    _minDate = widget.minDate;
    _maxDate = widget.maxDate;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: SfDateRangePicker(
            controller: _controller,
            selectionMode: DateRangePickerSelectionMode.range,
            initialSelectedRange: _selectedRange,
            minDate: _minDate,
            maxDate: _maxDate,
            onSelectionChanged: _onSelectionChanged,
            enableMultiView: true,
            viewSpacing: 10,
            backgroundColor: vpColor.backgroundElevation0,
            navigationMode: DateRangePickerNavigationMode.scroll,
            navigationDirection: DateRangePickerNavigationDirection.vertical,
            todayHighlightColor: vpColor.strokePrimary,
            onViewChanged: (arg) {},
            selectionColor: vpColor.backgroundBrand,
            allowViewNavigation: false,
            selectionTextStyle: vpTextStyle.subtitle14.copyColor(
              vpColor.textWhite,
            ),
            startRangeSelectionColor: vpColor.backgroundBrand,
            endRangeSelectionColor: vpColor.backgroundBrand,
            rangeTextStyle: vpTextStyle.subtitle14.copyColor(
              vpColor.textPrimary,
            ),
            headerStyle: DateRangePickerHeaderStyle(
              textStyle: vpTextStyle.captionSemiBold.copyColor(
                vpColor.textTertiary,
              ),
            ),
            rangeSelectionColor: vpColor.backgroundElevationMinus2,
            monthFormat: 'MMMM',
          ),
        ),
        const VPDividerView(),
        const SizedBox(height: 16),
        _VerticalDateBottomActionView(
          endDateAtEndOfDay: widget.endDateAtEndOfDay,
          selectedRange: _selectedRange,
          controller: _controller,
          onReset: _onReset,
        ),
      ],
    );
  }

  void _onSelectionChanged(DateRangePickerSelectionChangedArgs args) {
    final range = args.value as PickerDateRange?;

    if (range == null) {
      setState(() => _selectedRange = range);

      return;
    }

    if (!_userStartedSelection &&
        range.startDate != null &&
        widget.rangeDay != null) {
      final baseDate = range.startDate!;
      setState(() {
        _userStartedSelection = true;
        _minDate = baseDate.subtract(Duration(days: widget.rangeDay!));
        _maxDate = baseDate.add(Duration(days: widget.rangeDay!));
      });
    }

    if (range.startDate != null) {
      setState(() => _selectedRange = range);
    }
  }

  void _onReset() {
    setState(() {
      _controller.selectedRange = null;
      _userStartedSelection = false;
      _minDate = widget.minDate;
      _maxDate = widget.maxDate;
    });
  }
}

class _VerticalDateBottomActionView extends StatelessWidget {
  const _VerticalDateBottomActionView({
    required this.selectedRange,
    required this.controller,
    this.endDateAtEndOfDay = true,
    this.onReset,
  });

  final VoidCallback? onReset;

  final PickerDateRange? selectedRange;

  final DateRangePickerController controller;

  /// true: 2025-19-07 23:59:59
  /// false: 2025-19-07 00:00:00
  final bool endDateAtEndOfDay;

  bool get timeValid =>
      selectedRange?.startDate != null && selectedRange?.endDate != null;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(child: timeValid ? buildTimeView() : const SizedBox.shrink()),

        const SizedBox(width: 15),

        /// reset button
        VpsButton.teriatySmall(
          title: 'Đặt lại',
          onPressed: onReset,
        ),

        const SizedBox(width: 32),

        /// apply button
        VpsButton.primarySmall(
          title: 'Áp dụng',
          disabled: !timeValid,
          onPressed: () => Navigator.of(context).pop(
            (
              startDate: selectedRange?.startDate,
              endDate: endDateAtEndOfDay
                  ? selectedRange?.endDate
                      ?.copyWith(hour: 23, minute: 59, second: 59)
                  : selectedRange?.endDate,
            ),
          ),
        ),
      ],
    );
  }

  Widget buildTimeView() {
    final startDate = selectedRange?.startDate?.formatToDdMmYyyy();
    final endDate = selectedRange?.endDate?.formatToDdMmYyyy();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Ngày chọn',
          style: vpTextStyle.captionRegular.copyColor(
            vpColor.textSecondary,
          ),
        ),
        Text(
          '$startDate - $endDate',
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
        ),
      ],
    );
  }
}
