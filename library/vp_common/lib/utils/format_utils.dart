import 'dart:math';

import 'package:intl/intl.dart';
import 'package:vp_common/vp_common.dart';

class FormatUtils {
  static String? formatCurrency(
    num? value, {
    String currency = '',
    bool trimZero = false,
    bool showSign = false,
    bool showPositiveSign = false,
    int fractionDigits = 2,
    bool convertToThousand = true,
  }) {
    if (value == null) return null;

    final formattedValue = convertToThousand ? value / 1000 : value;

    final newFractionDigits =
        trimZero && formattedValue == formattedValue.roundToDouble()
            ? 0
            : fractionDigits;

    final format = NumberFormat.currency(
      locale: 'en-US',
      symbol: '',
      decimalDigits: newFractionDigits,
    );

    if (showSign) {
      final data = format.format(formattedValue);

      /// 0.0001
      /// 0.001
      if (num.tryParse(data) == 0) return '$data$currency';

      if (formattedValue > 0 && showPositiveSign) {
        return '+$data$currency';
      }

      return '$data$currency';
    }

    return '${format.format(formattedValue.abs())}$currency';
  }

  static String? formatNumberWithOneDecimal(
    num? number, {
    bool convertToThousand = false,
  }) {
    if (number == null) return null;

    var value = number;
    if (convertToThousand) value /= 1000;

    // Làm tròn đến 1 chữ số thập phân
    value = num.parse(value.toStringAsFixed(1));

    final format = NumberFormat('#,##0.0', 'en_US');
    final result = format.format(value);
    return result.trim();
  }

  static String? formatNumberWithTwoDecimals(
    num? number, {
    bool convertToThousand = false,
  }) {
    if (number == null) return null;

    var value = number;
    if (convertToThousand == true) value /= 1000;

    final format = NumberFormat('#,##0.00', 'en_US');
    final result = format.format(value);
    return result.trim();
  }

  static String? formatNumberMaxTwoDecimals(
    num? number, {
    bool convertToThousand = false,
  }) {
    if (number == null) return null;

    var value = number;
    if (convertToThousand == true) value /= 1000;

    final format = NumberFormat('#,###.##', 'en_US');
    final result = format.format(value);
    return result.trim();
  }

  static String? toFormat3(num? value) {
    if (value == null) return null;

    final format = NumberFormat('###,###,###', 'en_US');
    final result = format.format(value);
    return result.trim();
  }

  static String? toFormat4(num? value) {
    if (value == null) return null;

    if (value % 1 == 0) {
      final format = NumberFormat('###,###,##0.0', 'en_US');
      final result = format.format(value);
      return result.trim();
    }

    final format = NumberFormat('###,###,###.#', 'en_US');
    final result = format.format(value);
    return result.trim();
  }

  static String? formatClosePrice(
    num? value, {
    String currency = '',
    bool trimZero = false,
    bool showSign = false,
    bool convertToThousand = true,
    int fractionDigits = 2,
  }) {
    if (value == null) return null;

    var formattedValue = convertToThousand ? value / 1000 : value;

    final roundingFactor = pow(10, fractionDigits);
    formattedValue = (formattedValue * roundingFactor).round() / roundingFactor;

    final result =
        (trimZero && formattedValue == formattedValue.roundToDouble())
            ? formattedValue.abs().toInt().toString()
            : formattedValue.abs().toStringAsFixed(fractionDigits);

    if (showSign && formattedValue != 0) {
      if (formattedValue > 0) return '+$result$currency';
      if (formattedValue < 0) return '-$result$currency';
    }

    return '$result$currency';
  }

  static String? formatPercent(
    num? number, {
    int fractionDigits = 2,
    bool showSign = true,
    bool removeTrailingZeros = false,
    bool showPositiveSign = true,
  }) {
    if (number == null) return null;

    final absValue = number.abs();
    var formattedValue = (removeTrailingZeros && absValue == absValue.toInt())
        ? absValue.toInt().toString()
        : absValue.toStringAsFixed(fractionDigits);
    if (removeTrailingZeros && formattedValue.contains('.')) {
      formattedValue = formattedValue.replaceAll(RegExp(r'\.?0+$'), '');
    }

    if (showSign && number != 0) {
      if (number > 0 && showPositiveSign) return '+$formattedValue%';
      if (number < 0) return '-$formattedValue%';
    }
    return '$formattedValue%';
  }

  static String formatVolWithTrailing(num vol) {
    final format = NumberFormat('#,##0.00', 'en_US');

    if (vol >= 1000000000) {
      return '${format.format(vol / 1000000000)} tỷ';
    } else if (vol >= 1000000) {
      return '${format.format(vol / 1000000)} triệu';
    } else if (vol >= 1000) {
      return '${format.format(vol / 1000)} nghìn';
    } else {
      return format.format(vol);
    }
  }

  static String formatVol(
    num? value, {
    bool excludeZero = true,
  }) {
    if (value == null) {
      return '-';
    }

    if (value.isZero() && excludeZero) {
      return '-';
    }

    if (value.isOddLot) {
      return value.toInt().toString();
    }

    final volValue = value.toFormat3();
    final data = volValue.trim();

    return data;
  }
}
