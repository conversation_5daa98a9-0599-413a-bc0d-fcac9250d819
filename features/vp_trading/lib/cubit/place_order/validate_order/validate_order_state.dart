part of 'validate_order_cubit.dart';

class ValidateOrderState extends Equatable {
  final SessionType? sessionType;
  final ErrorPrice errorPrice;
  final ErrorVolume errorVolume;
  final FocusKeyboard focusKeyboard;
  final String calculateValue;
  final String? currentPrice;
  final String? currentVolume;
  const ValidateOrderState({
    this.sessionType,
    this.errorPrice = ErrorPrice.init,
    this.errorVolume = ErrorVolume.init,
    this.focusKeyboard = FocusKeyboard.none,
    this.calculateValue = '',
    this.currentPrice,
    this.currentVolume,
  });
  @override
  List<Object?> get props => [
    sessionType,
    errorPrice,
    errorVolume,
    focusKeyboard,
    calculateValue,
    currentPrice,
    currentVolume,
  ];

  ValidateOrderState copyWith({
    SessionType? sessionType,
    ErrorPrice? errorPrice,
    ErrorVolume? errorVolume,
    FocusKeyboard? focusKeyboard,
    String? calculateValue,
    String? currentPrice,
    String? currentVolume,
  }) {
    return ValidateOrderState(
      sessionType: sessionType ?? this.sessionType,
      errorPrice: errorPrice ?? this.errorPrice,
      errorVolume: errorVolume ?? this.errorVolume,
      focusKeyboard: focusKeyboard ?? this.focusKeyboard,
      calculateValue: calculateValue ?? this.calculateValue,
      currentPrice: currentPrice ?? this.currentPrice,
      currentVolume: currentVolume ?? this.currentVolume,
    );
  }

  ValidateOrderState clearSession() {
    return ValidateOrderState(
      sessionType: null,
      errorPrice: errorPrice,
      errorVolume: errorVolume,
      focusKeyboard: focusKeyboard,
      calculateValue: calculateValue,
      currentPrice: currentPrice,
      currentVolume: currentVolume,
    );
  }
}

extension ValidateOrderStateExtension on ValidateOrderState {
  bool get isErrorPrice => errorPrice != ErrorPrice.none && sessionType == null;
  bool get isValid {
    final isValidVolume =
        errorVolume == ErrorVolume.none && currentVolume?.isNotEmpty == true;
    if (sessionType != null) {
      return isValidVolume;
    }
    return isValidVolume &&
        errorPrice == ErrorPrice.none &&
        currentPrice?.isNotEmpty == true;
  }
}
