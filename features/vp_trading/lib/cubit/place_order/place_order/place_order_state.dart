part of 'place_order_cubit.dart';

class PlaceOrderState extends Equatable {
  final SubAccountType subAccountType;

  final OrderLotType orderLotType;

  final OrderAction action;

  final String symbol;

  final OrderType orderType;

  final bool isSaveCommand;

  const PlaceOrderState({
    required this.subAccountType,
    required this.symbol,
    this.action = OrderAction.buy,
    this.orderLotType = OrderLotType.roundLot,
    this.orderType = OrderType.lo,
    this.isSaveCommand = false,
  });

  PlaceOrderState copyWith({
    ApiStatus? apiStatus,
    String? symbol,
    OrderAction? action,
    OrderLotType? orderLotType,
    SubAccountType? subAccountType,
    OrderType? orderType,
    bool? isSaveCommand,
  }) {
    return PlaceOrderState(
      symbol: symbol ?? this.symbol,
      action: action ?? this.action,
      orderLotType: orderLotType ?? this.orderLotType,
      subAccountType: subAccountType ?? this.subAccountType,
      orderType: orderType ?? this.orderType,
      isSaveCommand: isSaveCommand ?? this.isSaveCommand,
    );
  }

  @override
  List<Object?> get props => [
    symbol,
    orderType,
    orderLotType,
    action,
    orderLotType,
    subAccountType,
    isSaveCommand,
  ];
}

extension PlaceOrderStateD on PlaceOrderState {
  bool get isLoOrGtc => orderType.isLoOrGtc;
  bool get isBuyIn => orderType == OrderType.buyIn;

  bool get isLoOrGtcOrBuyIn => isLoOrGtc || isBuyIn;
  //bool get isLoOrGtcOrBuyIn => isLoOrGtc || isChoiceConditionalOrder || isBuyIn;
}
