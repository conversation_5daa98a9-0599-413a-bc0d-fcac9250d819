import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_helper.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_trading/cubit/order_container/delete_update_order/delete_update_order_cubit.dart';
import 'package:vp_trading/cubit/order_container/delete_update_order/delete_update_order_state.dart';
import 'package:vp_trading/cubit/order_container/normal_order/normal_order_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/order/order_book_model.dart';
import 'package:vp_trading/model/request/order/delete_order_request.dart';
import 'package:vp_trading/screen/order_container/normal_order/widget/normal_order_item.dart';

class NormalOrderList extends StatefulWidget {
  final List<OrderBookModel> items;
  final bool hasMore;
  final Future<dynamic> Function()? loadMore;
  final Future<void> Function() refresh;
  final Future<void> Function() editSuccess;
  final bool? shrinkWrap;

  const NormalOrderList({
    super.key,
    required this.items,
    required this.hasMore,
    required this.loadMore,
    required this.refresh,
    this.shrinkWrap = false,
    required this.editSuccess,
  });

  @override
  State<NormalOrderList> createState() => _NormalOrderListState();
}

class _NormalOrderListState extends State<NormalOrderList> {
  late DeleteUpdateOrderCubit _deleteUpdateOrderCubit;

  @override
  void initState() {
    super.initState();
    _deleteUpdateOrderCubit = DeleteUpdateOrderCubit();
  }

  @override
  void dispose() {
    _deleteUpdateOrderCubit.close();
    super.dispose();
  }

  void _onEditCommand(OrderBookModel item) {
    widget.editSuccess();
  }

  void _onDeleteCommand(OrderBookModel item) {
    _deleteUpdateOrderCubit.deleteOrder(
      DeleteOrderRequest(
        orderId: item.orderId ?? '',
        accountId: item.accountId ?? '',
        market: "equity",
        requestId: "Prefix: ${AppHelper().genXRequestID()}",
        username: GetIt.instance<AuthCubit>().userInfo?.userinfo?.username,
        via: "V",
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _deleteUpdateOrderCubit,
      child: BlocListener<DeleteUpdateOrderCubit, DeleteUpdateOrderState>(
        listener: (context, state) {
          if (state.status == DeleteUpdateOrderStateEnum.isDeleteSuccess) {
            context.showSnackBar(
              content: VPTradingLocalize.current.trading_cancel_order_success,
              snackBarType: VPSnackBarType.success,
            );
            _deleteUpdateOrderCubit.resetIsDeleteSuccess();
            widget.refresh();
          } else if (state.status ==
              DeleteUpdateOrderStateEnum.isUpdateSuccess) {
            context.showSnackBar(
              content: VPTradingLocalize.current.trading_update_order_success,
              snackBarType: VPSnackBarType.success,
            );
            _deleteUpdateOrderCubit.resetIsUpdateSuccess();
            widget.refresh();
          } else if (state.errorMessage != null) {
            context.showSnackBar(
              content: state.errorMessage ?? "-",
              snackBarType: VPSnackBarType.error,
            );
            _deleteUpdateOrderCubit.resetErrorMessage();
          }
          _deleteUpdateOrderCubit.resetState();
        },
        child: ListViewHelper.separated(
          shrinkWrap: widget.shrinkWrap ?? false,
          itemBuilder: (context, index) {
            final data = widget.items[index];
            final key = '${data.symbol}${data.orderId}';
            return NormalOrderItem(
              key: ValueKey(key),
              item: data,
              onEditCommand: _onEditCommand,
              onDeleteCommand: _onDeleteCommand,
            );
          },
          itemCount: () => widget.items.length,
          hasMore: () => widget.hasMore,
          loadMore: widget.loadMore,
          refresh: widget.refresh,
          separatorBuilder: (context, index) => const SizedBox(height: 8),
        ),
      ),
    );
  }
}
