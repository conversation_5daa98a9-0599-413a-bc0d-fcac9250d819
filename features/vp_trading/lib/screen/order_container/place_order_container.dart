import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/order_container/normal_order/normal_order_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/screen/order_container/normal_order/widget/normal_order_list.dart';
import 'package:vp_trading/screen/order_container/normal_order/widget/normal_order_title_widget.dart';

class PlaceOrderContainer extends StatefulWidget {
  const PlaceOrderContainer({super.key, required this.subAccountType});
  final SubAccountType subAccountType;

  @override
  State<PlaceOrderContainer> createState() => _PlaceOrderContainerState();
}

class _PlaceOrderContainerState extends State<PlaceOrderContainer> {
  bool _isOrderListVisible = true;

  void _toggleOrderList() {
    setState(() {
      _isOrderListVisible = !_isOrderListVisible;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (_) =>
              NormalOrderCubit()..initForWaitingOrders(widget.subAccountType),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            _header(),
            const SizedBox(height: 8),
            const DividerWidget(),
            AnimatedSize(
              duration: const Duration(milliseconds: 300),
              child:
                  _isOrderListVisible
                      ? _buildNormalOrderList()
                      : const SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }

  Row _header() {
    return Row(
      children: [
        BlocBuilder<NormalOrderCubit, NormalOrderState>(
          builder: (context, state) {
            return GestureDetector(
              onTap: _toggleOrderList,
              child: Text(
                "${VPTradingLocalize.current.trading_pending_order} (${state.listItems.length})",
                style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
              ),
            );
          },
        ),
        const SizedBox(width: 8),
        GestureDetector(
          onTap: _toggleOrderList,
          child: AnimatedRotation(
            turns: _isOrderListVisible ? 0.5 : 0.0,
            duration: const Duration(milliseconds: 200),
            child: DesignAssets.icons.dropdown.icArrowBottom.svg(
              colorFilter: ColorFilter.mode(
                vpColor.iconPrimary,
                BlendMode.srcIn,
              ),
            ),
          ),
        ),
        const Spacer(),
        Row(
          children: [
            GestureDetector(
              onTap: () {
                context.pushReplacement('/mainStockHome?initialPage=2');
              },
              child: Text(
                VPTradingLocalize.current.trading_command_history,
                style: vpTextStyle.captionMedium.copyColor(vpColor.textBrand),
              ),
            ),
          ],
        ),
      ],
    );
  }

  BlocBuilder<NormalOrderCubit, NormalOrderState> _buildNormalOrderList() {
    return BlocBuilder<NormalOrderCubit, NormalOrderState>(
      builder: (context, state) {
        return Column(
          children: [
            if (!state.isLoading && state.listItems.isEmpty)
              NoDataView(
                content: VPTradingLocalize.current.trading_no_data_message,
              ),
            if (state.listItems.isNotEmpty) ...[
              const ConditionNormalTitle(expandTitleWidget: [10, 6, 7, 14]),
              const SizedBox(height: 16),
              NormalOrderList(
                shrinkWrap: true,
                items: state.listItems,
                hasMore: state.hasMore,
                loadMore: () async {},
                refresh: () async {},
                editSuccess: () async =>  context.read<NormalOrderCubit>().initForWaitingOrders(widget.subAccountType),
              ),
            ],
          ],
        );
      },
    );
  }
}
