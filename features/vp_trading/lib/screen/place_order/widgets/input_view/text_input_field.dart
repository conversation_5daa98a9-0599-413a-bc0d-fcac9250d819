import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/place_order/place_order/place_order_cubit.dart';
import 'package:vp_trading/cubit/place_order/stock_info/stock_info_cubit.dart';
import 'package:vp_trading/cubit/place_order/validate_order/validate_order_cubit.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_box.dart';
import 'package:vp_trading/utils/text_inputformater.dart';
import 'package:vp_trading/vp_trading.dart';

class TextInputField extends StatefulWidget {
  final TextEditingController priceController;
  final TextEditingController volumeController;
  const TextInputField({
    super.key,
    required this.priceController,
    required this.volumeController,
  });

  @override
  State<TextInputField> createState() => TextInputFieldState();
}

class TextInputFieldState extends State<TextInputField> {
  late final StreamSubscription<String> _subscriptionPrice;
  late final StreamSubscription<String> _subscriptionSocketPrice;

  final ValueNotifier<bool> _priceBlink = ValueNotifier(false);
  final ValueNotifier<bool> _volumeBlink = ValueNotifier(false);
  final _priceFocusNode = FocusNode();
  final _volumeFocusNode = FocusNode();

  void _focusListener() {
    if (_priceFocusNode.hasFocus) {
      context.read<StockInfoCubit>().onRealtimeChanged(false);
      context.read<ValidateOrderCubit>().focusField(FocusKeyboard.price);
    } else if (_volumeFocusNode.hasFocus) {
      context.read<ValidateOrderCubit>().focusField(FocusKeyboard.volume);
    } else {
      context.read<ValidateOrderCubit>().focusField(FocusKeyboard.none);
    }
  }

  @override
  void initState() {
    super.initState();
    _priceFocusNode.addListener(_focusListener);
    _volumeFocusNode.addListener(_focusListener);
    if (widget.priceController.text.isNotEmpty) {
      context.read<ValidateOrderCubit>().onChangePrice(
        widget.priceController.text,
      );
    }
    _subscriptionPrice = context.read<PlaceOrderCubit>().priceStream.listen((
      data,
    ) {
      if (!mounted) return;

      if (context.read<ValidateOrderCubit>().state.sessionType == null) {
        context.read<ValidateOrderCubit>().onChangePrice(data);
      } else {
        context.read<ValidateOrderCubit>().clearSession();
        context.read<ValidateOrderCubit>().onChangePrice(data);
      }
      if (data.isEmpty) {
        context.read<ValidateOrderCubit>().onChangePrice("");
        context.read<ValidateOrderCubit>().onChangeVolumne("");
      } else {
        context.read<StockInfoCubit>().onRealtimeChanged(false);
      }
      //  context.read<ValidateOrderCubit>().clearSession();
    });
    _subscriptionSocketPrice = context
        .read<StockInfoCubit>()
        .priceStream
        .listen((data) {
          if (!mounted) return;
          if (context.read<ValidateOrderCubit>().state.sessionType == null) {
            context.read<ValidateOrderCubit>().onChangePrice(data);
          } else {
            context.read<ValidateOrderCubit>().clearSession();
            context.read<ValidateOrderCubit>().onChangePrice(data);
          }
        });
  }

  @override
  void dispose() {
    _priceFocusNode.dispose();
    _volumeFocusNode.dispose();
    _priceBlink.dispose();
    _volumeBlink.dispose();
    _subscriptionPrice.cancel();
    _subscriptionSocketPrice.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<ValidateOrderCubit, ValidateOrderState>(
          listenWhen:
              (previous, current) =>
                  previous.currentVolume != current.currentVolume,
          listener: (context, state) {
            if (state.currentVolume != null) {
              widget.volumeController.text = state.currentVolume!;
              widget.volumeController.selection = TextSelection.fromPosition(
                TextPosition(offset: widget.volumeController.text.length),
              );
            }
          },
        ),
        BlocListener<ValidateOrderCubit, ValidateOrderState>(
          listenWhen:
              (previous, current) =>
                  previous.currentPrice != current.currentPrice,
          listener: (context, state) {
            if (state.currentPrice != null) {
              widget.priceController.text = state.currentPrice!;
              widget.priceController.selection = TextSelection.fromPosition(
                TextPosition(offset: widget.priceController.text.length),
              );
            }
          },
        ),
      ],
      child: BlocBuilder<ValidateOrderCubit, ValidateOrderState>(
        builder: (context, state) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: ColoredBox(
                color: themeData.highlightBg,
                child: Row(
                  children: [
                    Expanded(
                      child: Blink(
                        blink: _priceBlink,
                        child: InputFieldBox(
                          onTapField: () {
                            if (state.sessionType != null) {
                              context.read<ValidateOrderCubit>().clearSession();
                              _priceFocusNode.requestFocus();
                            }
                          },
                          isError:
                              state.errorPrice.isError &&
                              widget.priceController.text.isNotEmpty,
                          sessionValue: state.sessionType?.name.toUpperCase(),
                          controller: widget.priceController,
                          hintText: 'Giá',
                          onChange: (value) {
                            context.read<StockInfoCubit>().onRealtimeChanged(
                              false,
                            );
                            context.read<ValidateOrderCubit>().onChangePrice(
                              value,
                            );
                          },
                          focusNode: _priceFocusNode,
                          onTap: (increase) {
                            context.read<ValidateOrderCubit>().priceTap(
                              text: widget.priceController.text,
                              increase: increase,
                            );
                          },
                          inputFormatters: [
                            removeZeroStartInputFormatter,
                            LengthLimitingTextInputFormatter(8),
                            ...priceInputFormatter,
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Blink(
                        blink: _volumeBlink,
                        child: InputFieldBox(
                          isError:
                              state.errorVolume.isError &&
                              widget.volumeController.text.isNotEmpty,

                          controller: widget.volumeController,
                          maxLength: 12,
                          hintText: 'KL',
                          onChange: (value) {
                            context.read<ValidateOrderCubit>().onChangeVolumne(
                              value,
                            );
                          },
                          focusNode: _volumeFocusNode,
                          scrollPadding: 180,
                          onTap: (increase) {
                            context.read<ValidateOrderCubit>().volumneTap(
                              text: widget.volumeController.text,
                              increase: increase,
                            );
                          },
                          inputFormatters: [
                            removeZeroStartInputFormatter,
                            ...volumeInputFormatter,
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
