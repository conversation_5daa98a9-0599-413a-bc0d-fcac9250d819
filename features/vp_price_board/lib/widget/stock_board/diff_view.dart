part of 'stock_board_item_view.dart';

class _DiffView extends StatelessWidget {
  const _DiffView({required this.item});

  final StockInfoModel item;

  @override
  Widget build(BuildContext context) {
    return VPSocketInvestmentBuilder<VPMarketInfoData>(
      symbol: item.symbol,
      channel: VPSocketChannel.marketData.name,
      buildWhen:
          (preData, data) =>
              preData?.marketIndex != data?.marketIndex &&
              data?.marketIndex != null,
      builder: (_, preData, marketData, child) {
        final marketIndex = marketData?.marketIndex;

        return VPSocketInvestmentBuilder<VPStockInfoData>(
          symbol: item.symbol,
          channel: VPSocketChannel.stockInfo.name,
          buildWhen:
              (preData, data) => StockBoardItemHelper.buildWhen(
                type: StockInfoFieldType.closePrice,
                item: item,
                preData: preData,
                data: data,
              ),
          builder: (_, preData, data, child) {
            final closePrice = StockBoardItemHelper.getClosePrice(
              item: item,
              data: data,
            );

            var diff = item.diff;
            if (marketIndex != null && closePrice != null) {
              diff = closePrice - marketIndex;
            }

            return Text(
              diff == null
                  ? '-'
                  : StockBoardUIHelper.getText(
                    value: FormatUtils.formatCurrency(
                      diff,
                      showSign: true,
                      convertToThousand: false,
                    ),
                    item: item,
                    socketData: data,
                  ),
              textAlign: TextAlign.end,
              style: vpTextStyle.subtitle14.copyColor(
                closePrice == 0 || closePrice == null
                    ? vpColor.textPrimary
                    : StockColorUtils.derivativeDiffColor(diff: diff),
              ),
            );
          },
        );
      },
    );
  }
}
