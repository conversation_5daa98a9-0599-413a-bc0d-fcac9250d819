part of 'stock_board_item_view.dart';

class _PriceChangeView extends StatelessWidget {
  const _PriceChangeView({required this.item});

  final StockInfoModel item;

  @override
  Widget build(BuildContext context) {
    return VPSocketInvestmentBuilder<VPStockInfoData>(
      symbol: item.symbol,
      channel: VPSocketChannel.stockInfo.name,
      buildWhen:
          (preData, data) => StockBoardItemHelper.buildWhen(
            type: StockInfoFieldType.change,
            item: item,
            preData: preData,
            data: data,
          ),
      builder: (_, preData, data, child) {
        final change =
            StockBoardItemHelper.getChange(item: item, data: data) ?? 0;

        final changeValue = FormatUtils.formatCurrency(
          change,
          showSign: true,
          showPositiveSign: false,
          convertToThousand: item.stockType?.isFU != true,
        );

        final textColor = StockBoardUIHelper.getTextColor(
          item: item,
          data: data,
        );

        return Text(
          StockBoardUIHelper.getText(
            value: changeValue,
            item: item,
            socketData: data,
          ),
          textAlign: TextAlign.end,
          style: vpTextStyle.subtitle14.copyColor(textColor),
        );
      },
    );
  }
}
