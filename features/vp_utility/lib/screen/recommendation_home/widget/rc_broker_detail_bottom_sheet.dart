import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/model/enum/order_action.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_utility/cubit/recommendation_home/detail_broker_rc/detail_broker_rc_cubit.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/model/response/recommendation/detail_broker_rc_model.dart';
import 'package:vp_utility/screen/recommendation_home/enum/type_rc_enum.dart';
import 'package:vp_utility/screen/recommendation_home/widget/title_content_widget.dart';

class RecommendationDetailBottomSheet extends StatefulWidget {
  const RecommendationDetailBottomSheet({
    super.key,
    required this.stockOrderId,
  });

  final int stockOrderId;

  @override
  State<RecommendationDetailBottomSheet> createState() =>
      _RecommendationDetailBottomSheetState();
}

class _RecommendationDetailBottomSheetState
    extends State<RecommendationDetailBottomSheet> {
  ThemeData get colorUtils => Theme.of(context);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (context) => DetailBrokerRCCubit()..getDetail(widget.stockOrderId),
      child: BlocConsumer<DetailBrokerRCCubit, DetailBrokerRCState>(
        listener: (context, state) {},
        builder: (context, state) {
          if (state.isLoading) {
            return const Center(child: VPBankLoading());
          }
          var data = state.data ?? DetailBrokerRCModel();
          return SafeArea(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 16),
                RcInfoHeader(model: data),
                SizedBox(height: 8),

                TitleContentWidget(
                  title: S.current.rc_typeRecommendation,
                  content: data.typeRcEnum.localizedText.toUpperCase(),
                  contentColor: data.typeRcEnum.color,
                ),
                SizedBox(height: 8),
                TitleContentWidget(
                  title: S.current.rc_suggestedVolume,
                  content: data.volume,
                ),
                SizedBox(height: 8),
                TitleContentWidget(
                  title: S.current.rc_suggestedPrice,
                  content: data.minMaxRecommendedPrice,
                ),
                if (data.typeRcEnum == TypeRcEnum.buy) ...[
                  SizedBox(height: 8),
                  TitleContentWidget(
                    title: S.current.rc_takeProfit,
                    content:
                        FormatUtils.formatNumberWithTwoDecimals(
                          double.tryParse(data.takeProfitPrice ?? ''),
                        ) ??
                        '-',
                    contentColor: colorUtils.primary,
                  ),
                  SizedBox(height: 8),
                  TitleContentWidget(
                    title: S.current.rc_stopLoss,
                    content:
                        FormatUtils.formatNumberWithTwoDecimals(
                          double.tryParse(data.stopLossPrice ?? ''),
                        ) ??
                        '-',
                    contentColor: colorUtils.red,
                  ),
                ],
                if (data.expiredDate != null) ...[
                  SizedBox(height: 8),
                  TitleContentWidget(
                    title: S.current.rc_validTill,
                    content: data.expiredDate!.formatToDdMmYyyy(),
                  ),
                ],
                SizedBox(height: 24),

                Divider(height: 1, color: colorUtils.divider),
                if (data.createdDate != null) ...[
                  SizedBox(height: 24),
                  TitleContentWidget(
                    title: S.current.rc_recommendedDate,
                    content: data.createdDate!.formatToDdMmYyyy(),
                  ),
                ],
                if (data.createdByName != null) ...[
                  SizedBox(height: 8),
                  TitleContentWidget(
                    title: S.current.rc_recommendedPerson,
                    content: data.createdByName!,
                  ),
                ],
                SizedBox(height: 8),
                Text(
                  data.note ?? '',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  data.noteDetail ?? '',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w400),
                ),

                SizedBox(height: 24),
                VPDynamicButton.primarySmall(
                  text: data.keyLangButton,
                  onTap: () {
                    _navigateToOrder(data);
                  },
                  backgroundColor: data.colorButton,
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  _navigateToOrder(DetailBrokerRCModel data) {
    context.pop();
    context.pushNamed(
      '/placeOrder',
      queryParameters:
          PlaceOrderArgs(
            symbol: data.symbol ?? "VPB",
            action:
                data.typeRcEnum == TypeRcEnum.sell
                    ? OrderAction.sell
                    : OrderAction.buy,
          ).toQueryParams(),
    );
  }
}

class RcInfoHeader extends StatelessWidget {
  final DetailBrokerRCModel model;

  const RcInfoHeader({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    final colorUtils = Theme.of(context);
    return Row(
      children: [
        Expanded(
          child: Text(
            model.symbol ?? '',
            style: context.textStyle.subtitle14?.copyWith(
              color: vpColor.textPrimary,
            ),
          ),
        ),
        SizedBox(width: 16),
        Container(
          padding: const EdgeInsets.all(4).copyWith(bottom: 6),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: model.isActive ? colorUtils.primary16 : colorUtils.red16,
          ),
          child: Text(
            model.isActive ? S.current.rc_active : S.current.rc_status_expired,
            style: context.textStyle.captionRegular?.copyWith(
              color: vpColor.textWhite,
            ),
          ),
        ),
      ],
    );
  }
}
