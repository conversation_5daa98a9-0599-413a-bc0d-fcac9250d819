enum RecommendationType { vp<PERSON><PERSON>, consultingExpert, broker, }

extension RecommendationTypeExtention on RecommendationType {
  String get title {
    switch (this) {
      case RecommendationType.vpbanks:
        return 'VPBankS';
      case RecommendationType.consultingExpert:
        return '<PERSON>yên gia tư vấn';
      case RecommendationType.broker:
        return 'Broker';
    }
  }

  String get dataServer {
    switch (this) {
      case RecommendationType.vpbanks:
        return 'NCP,CLTT';
      case RecommendationType.consultingExpert:
        return 'KDS';
      case RecommendationType.broker:
        return '';
    }
  }
   static List<RecommendationType> valuesByHasBroker(bool hasBroker) {
    if (hasBroker) {
      return [RecommendationType.vpbanks, RecommendationType.broker];
    } else {
      return [RecommendationType.vpbanks, RecommendationType.consultingExpert];
    }
  }
}

 