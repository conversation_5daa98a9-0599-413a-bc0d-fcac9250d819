// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'broker_filter_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$BrokerFilterState {

 RcParamFilter? get stockFilter; RcParamFilter? get generalFilter;
/// Create a copy of BrokerFilterState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BrokerFilterStateCopyWith<BrokerFilterState> get copyWith => _$BrokerFilterStateCopyWithImpl<BrokerFilterState>(this as BrokerFilterState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BrokerFilterState&&(identical(other.stockFilter, stockFilter) || other.stockFilter == stockFilter)&&(identical(other.generalFilter, generalFilter) || other.generalFilter == generalFilter));
}


@override
int get hashCode => Object.hash(runtimeType,stockFilter,generalFilter);

@override
String toString() {
  return 'BrokerFilterState(stockFilter: $stockFilter, generalFilter: $generalFilter)';
}


}

/// @nodoc
abstract mixin class $BrokerFilterStateCopyWith<$Res>  {
  factory $BrokerFilterStateCopyWith(BrokerFilterState value, $Res Function(BrokerFilterState) _then) = _$BrokerFilterStateCopyWithImpl;
@useResult
$Res call({
 RcParamFilter? stockFilter, RcParamFilter? generalFilter
});




}
/// @nodoc
class _$BrokerFilterStateCopyWithImpl<$Res>
    implements $BrokerFilterStateCopyWith<$Res> {
  _$BrokerFilterStateCopyWithImpl(this._self, this._then);

  final BrokerFilterState _self;
  final $Res Function(BrokerFilterState) _then;

/// Create a copy of BrokerFilterState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? stockFilter = freezed,Object? generalFilter = freezed,}) {
  return _then(_self.copyWith(
stockFilter: freezed == stockFilter ? _self.stockFilter : stockFilter // ignore: cast_nullable_to_non_nullable
as RcParamFilter?,generalFilter: freezed == generalFilter ? _self.generalFilter : generalFilter // ignore: cast_nullable_to_non_nullable
as RcParamFilter?,
  ));
}

}


/// Adds pattern-matching-related methods to [BrokerFilterState].
extension BrokerFilterStatePatterns on BrokerFilterState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _BrokerFilterState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _BrokerFilterState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _BrokerFilterState value)  $default,){
final _that = this;
switch (_that) {
case _BrokerFilterState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _BrokerFilterState value)?  $default,){
final _that = this;
switch (_that) {
case _BrokerFilterState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( RcParamFilter? stockFilter,  RcParamFilter? generalFilter)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _BrokerFilterState() when $default != null:
return $default(_that.stockFilter,_that.generalFilter);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( RcParamFilter? stockFilter,  RcParamFilter? generalFilter)  $default,) {final _that = this;
switch (_that) {
case _BrokerFilterState():
return $default(_that.stockFilter,_that.generalFilter);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( RcParamFilter? stockFilter,  RcParamFilter? generalFilter)?  $default,) {final _that = this;
switch (_that) {
case _BrokerFilterState() when $default != null:
return $default(_that.stockFilter,_that.generalFilter);case _:
  return null;

}
}

}

/// @nodoc


class _BrokerFilterState implements BrokerFilterState {
  const _BrokerFilterState({this.stockFilter, this.generalFilter});
  

@override final  RcParamFilter? stockFilter;
@override final  RcParamFilter? generalFilter;

/// Create a copy of BrokerFilterState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BrokerFilterStateCopyWith<_BrokerFilterState> get copyWith => __$BrokerFilterStateCopyWithImpl<_BrokerFilterState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BrokerFilterState&&(identical(other.stockFilter, stockFilter) || other.stockFilter == stockFilter)&&(identical(other.generalFilter, generalFilter) || other.generalFilter == generalFilter));
}


@override
int get hashCode => Object.hash(runtimeType,stockFilter,generalFilter);

@override
String toString() {
  return 'BrokerFilterState(stockFilter: $stockFilter, generalFilter: $generalFilter)';
}


}

/// @nodoc
abstract mixin class _$BrokerFilterStateCopyWith<$Res> implements $BrokerFilterStateCopyWith<$Res> {
  factory _$BrokerFilterStateCopyWith(_BrokerFilterState value, $Res Function(_BrokerFilterState) _then) = __$BrokerFilterStateCopyWithImpl;
@override @useResult
$Res call({
 RcParamFilter? stockFilter, RcParamFilter? generalFilter
});




}
/// @nodoc
class __$BrokerFilterStateCopyWithImpl<$Res>
    implements _$BrokerFilterStateCopyWith<$Res> {
  __$BrokerFilterStateCopyWithImpl(this._self, this._then);

  final _BrokerFilterState _self;
  final $Res Function(_BrokerFilterState) _then;

/// Create a copy of BrokerFilterState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? stockFilter = freezed,Object? generalFilter = freezed,}) {
  return _then(_BrokerFilterState(
stockFilter: freezed == stockFilter ? _self.stockFilter : stockFilter // ignore: cast_nullable_to_non_nullable
as RcParamFilter?,generalFilter: freezed == generalFilter ? _self.generalFilter : generalFilter // ignore: cast_nullable_to_non_nullable
as RcParamFilter?,
  ));
}


}

// dart format on
