import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vp_utility/model/request/rc_param_filtter.dart';

part 'broker_filter_state.freezed.dart';

@freezed
abstract class BrokerFilterState with _$BrokerFilterState {
  const factory BrokerFilterState({
    RcParamFilter? stockFilter,
    RcParamFilter? generalFilter,
  }) = _BrokerFilterState;

  factory BrokerFilterState.initial() => const BrokerFilterState();
}

extension BrokerFilterStateExtension on BrokerFilterState {
  RcParamFilter? getFilterByIndex(int tabIndex) {
    return tabIndex == 0 ? stockFilter : generalFilter;
  }

  bool hasFilterForIndex(int tabIndex) {
    final filter = getFilterByIndex(tabIndex);
    return filter != null;
  }
}
