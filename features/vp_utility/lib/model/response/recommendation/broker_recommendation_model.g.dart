// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'broker_recommendation_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BrokerRecommendationModel _$BrokerRecommendationModelFromJson(
  Map<String, dynamic> json,
) => BrokerRecommendationModel(
  reFullName: json['reFullName'] as String?,
  reCustodyCd: json['reCustodyCd'] as String?,
  dept: json['dept'] as String?,
  refername: json['refername'] as String?,
  refercustodycd: json['refercustodycd'] as String?,
  referdept: json['referdept'] as String?,
  stockOrderId: json['stockOrderId'] as String?,
  expiredDate: json['expiredDate'] as String?,
  timeBooking: json['timeBooking'] as String?,
  side: json['side'] as String?,
  status: json['statusRc'] as String?,
  symbol: json['symbol'] as String?,
  minRecommendedPrice: (json['minRecommendedPrice'] as num?)?.toDouble(),
  maxRecommendedPrice: (json['maxRecommendedPrice'] as num?)?.toDouble(),
  createdDate: json['createdDate'] as String?,
  source: json['source'] as String?,
);

Map<String, dynamic> _$BrokerRecommendationModelToJson(
  BrokerRecommendationModel instance,
) => <String, dynamic>{
  'reFullName': instance.reFullName,
  'reCustodyCd': instance.reCustodyCd,
  'dept': instance.dept,
  'refername': instance.refername,
  'refercustodycd': instance.refercustodycd,
  'referdept': instance.referdept,
  'stockOrderId': instance.stockOrderId,
  'expiredDate': instance.expiredDate,
  'timeBooking': instance.timeBooking,
  'side': instance.side,
  'statusRc': instance.status,
  'symbol': instance.symbol,
  'minRecommendedPrice': instance.minRecommendedPrice,
  'maxRecommendedPrice': instance.maxRecommendedPrice,
  'createdDate': instance.createdDate,
  'source': instance.source,
};
