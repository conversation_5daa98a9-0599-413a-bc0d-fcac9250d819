// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_broker_rc_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DetailBrokerRCModel _$DetailBrokerRCModelFromJson(Map<String, dynamic> json) =>
    DetailBrokerRCModel(
      id: (json['id'] as num?)?.toInt(),
      side: json['side'] as String?,
      symbol: json['symbol'] as String?,
      minRecommendedPrice: json['minRecommendedPrice'] as String?,
      maxRecommendedPrice: json['maxRecommendedPrice'] as String?,
      takeProfitPrice: json['takeProfitPrice'] as String?,
      stopLossPrice: json['stopLossPrice'] as String?,
      recommendedRateType: json['recommendedRateType'] as String?,
      recommendedRateValue: (json['recommendedRateValue'] as num?)?.toDouble(),
      expiredDate:
          json['expiredDate'] == null
              ? null
              : DateTime.parse(json['expiredDate'] as String),
      createdDate:
          json['createdDate'] == null
              ? null
              : DateTime.parse(json['createdDate'] as String),
      lastModifiedDate:
          json['lastModifiedDate'] == null
              ? null
              : DateTime.parse(json['lastModifiedDate'] as String),
      status: json['status'] as String?,
      note: json['note'] as String?,
      noteDetail: json['noteDetail'] as String?,
      createdBy: json['createdBy'] as String?,
      lastModifiedBy: json['lastModifiedBy'] as String?,
      createdByName: json['createdByName'] as String?,
    );

Map<String, dynamic> _$DetailBrokerRCModelToJson(
  DetailBrokerRCModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'side': instance.side,
  'symbol': instance.symbol,
  'minRecommendedPrice': instance.minRecommendedPrice,
  'maxRecommendedPrice': instance.maxRecommendedPrice,
  'takeProfitPrice': instance.takeProfitPrice,
  'stopLossPrice': instance.stopLossPrice,
  'recommendedRateType': instance.recommendedRateType,
  'recommendedRateValue': instance.recommendedRateValue,
  'expiredDate': instance.expiredDate?.toIso8601String(),
  'createdDate': instance.createdDate?.toIso8601String(),
  'lastModifiedDate': instance.lastModifiedDate?.toIso8601String(),
  'status': instance.status,
  'note': instance.note,
  'noteDetail': instance.noteDetail,
  'createdBy': instance.createdBy,
  'lastModifiedBy': instance.lastModifiedBy,
  'createdByName': instance.createdByName,
};
