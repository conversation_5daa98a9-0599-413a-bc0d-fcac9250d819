import 'package:json_annotation/json_annotation.dart';
import 'package:vp_common/utils/format_utils.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/screen/recommendation_home/enum/type_rc_enum.dart';

part 'broker_recommendation_model.g.dart';

@JsonSerializable()
class BrokerRecommendationModel {
  final String? reFullName;
  final String? reCustodyCd;
  final String? dept;
  final String? refername;
  final String? refercustodycd;
  final String? referdept;
  final String? stockOrderId;
  final String? expiredDate;
  final String? timeBooking;
  final String? side;
  @J<PERSON><PERSON><PERSON>(name: 'statusRc')
  final String? status;
  final String? symbol;
  final double? minRecommendedPrice;
  final double? maxRecommendedPrice;
  final String? createdDate;
  final String? source;

  BrokerRecommendationModel({
    this.reFullName,
    this.reCustodyCd,
    this.dept,
    this.refername,
    this.refercustodycd,
    this.referdept,
    this.stockOrderId,
    this.expiredDate,
    this.timeBooking,
    this.side,
    this.status,
    this.symbol,
    this.minRecommendedPrice,
    this.maxRecommendedPrice,
    this.createdDate,
    this.source,
  });

  factory BrokerRecommendationModel.fromJson(Map<String, dynamic> json) =>
      _$BrokerRecommendationModelFromJson(json);

  Map<String, dynamic> toJson() => _$BrokerRecommendationModelToJson(this);
}

extension BrokerRecommendationModelExts on BrokerRecommendationModel {
  TypeRcEnum get typeRcEnum => TypeRcEnumExtension.parseTypeRcEnum(side);

  String get minMaxRecommendedPrice {
    if (minRecommendedPrice == null) {
      return '${S.current.rc_less} $maxRecommendedPrice';
    } else if (maxRecommendedPrice == null) {
      return '${S.current.rc_bigger} $minRecommendedPrice';
    }
    return '${FormatUtils.formatNumberWithTwoDecimals(minRecommendedPrice ?? 0)} - ${FormatUtils.formatNumberWithTwoDecimals(maxRecommendedPrice ?? 0)}';
  }
}
