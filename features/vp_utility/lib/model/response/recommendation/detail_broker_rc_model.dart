import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:vp_common/utils/money_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_utility/generated/l10n.dart';
import 'package:vp_utility/screen/recommendation_home/enum/type_rc_enum.dart';

part 'detail_broker_rc_model.g.dart';

@JsonSerializable()
class DetailBrokerRCModel {
  final int? id;
  final String? side;
  final String? symbol;
  final String? minRecommendedPrice;
  final String? maxRecommendedPrice;
  final String? takeProfitPrice;
  final String? stopLossPrice;
  final String? recommendedRateType;
  final double? recommendedRateValue;
  final DateTime? expiredDate;
  final DateTime? createdDate;
  final DateTime? lastModifiedDate;
  final String? status;
  final String? note;
  final String? noteDetail;
  final String? createdBy;
  final String? lastModifiedBy;
  final String? createdByName;

  DetailBrokerRCModel({
    this.id,
    this.side,
    this.symbol,
    this.minRecommendedPrice,
    this.maxRecommendedPrice,
    this.takeProfitPrice,
    this.stopLossPrice,
    this.recommendedRateType,
    this.recommendedRateValue,
    this.expiredDate,
    this.createdDate,
    this.lastModifiedDate,
    this.status,
    this.note,
    this.noteDetail,
    this.createdBy,
    this.lastModifiedBy,
    this.createdByName,
  });

  factory DetailBrokerRCModel.fromJson(Map<String, dynamic> json) =>
      _$DetailBrokerRCModelFromJson(json);
  Map<String, dynamic> toJson() => _$DetailBrokerRCModelToJson(this);
}

extension DetailBrokerRCModelExts on DetailBrokerRCModel {
  bool get isActive => status == 'ACTIVE';

  bool get isVolume => recommendedRateType == 'VOLUME';

  String get minMaxRecommendedPrice {
    if (minRecommendedPrice == null) {
      return '${S.current.rc_less} $maxRecommendedPrice';
    } else if (maxRecommendedPrice == null) {
      return '${S.current.rc_bigger} $minRecommendedPrice';
    }
    return '${FormatUtils.formatNumberWithTwoDecimals(double.tryParse(minRecommendedPrice ?? ''))} - ${FormatUtils.formatNumberWithTwoDecimals(double.tryParse(maxRecommendedPrice ?? ''))}';
  }

  TypeRcEnum get typeRcEnum => TypeRcEnumExtension.parseTypeRcEnum(side);

  String get volume {
    if (recommendedRateValue == null) {
      return '-';
    } else {
      if (isVolume) {
        return MoneyUtils.formatMoney(recommendedRateValue ?? 0.0, suffix: '');
      } else {
        if (typeRcEnum == TypeRcEnum.buy) {
          return '$recommendedRateValue% ${S.current.rc_purchasingAbility}';
        } else {
          return '$recommendedRateValue% ${S.current.rc_hold}';
        }
      }
    }
  }

  bool get checkEnableButton {
    if (isActive ?? false) {
      if (typeRcEnum == TypeRcEnum.buy) {
        return true;
      } else {
        // todo: check cổ phiếu trong cache
        return true;
        // return AppData().allStocks.firstWhereOrNull(
        //       (stock) => stock.symbol == symbol,
        //     ) !=
        //     null;
      }
    } else {
      return false;
    }
  }

  String get keyLangButton {
    if (typeRcEnum == TypeRcEnum.buy) {
      return S.current.rc_buy;
    } else {
      return S.current.rc_sell;
    }
  }

  Color get colorButton {
    if (typeRcEnum == TypeRcEnum.buy) {
      return Theme.of(getContext).primary;
    } else {
      return Theme.of(getContext).red;
    }
  }
}
